'use server';

const env = process.env;

// Server configuration
export async function getServerConfig() {
	return {
		port: Number.parseInt(env.PORT || '5000', 10),
		env: env.NODE_ENV || 'development',
	};
}

// Authentication configuration
export async function getAuthConfig() {
	return {
		jwtCookieName: env.JWT_COOKIE_NAME || 'auth_token',
		jwtSecret: env.JWT_SECRET || 'your-secret-key',
		jwtExpiresIn: Number(env.JWT_EXPIRES_IN) || 30 * 24 * 60 * 60,
		// Google OAuth configuration
		google: {
			clientId: env.GOOGLE_CLIENT_ID || '',
			clientSecret: env.GOOGLE_CLIENT_SECRET || '',
		},
	};
}

// Feature flags configuration
export async function getFeatureFlags() {
	return {
		googleLogin: env.FEATURE_GOOGLE_LOGIN === 'true',
	};
}

// LLM configuration
export async function getLLMConfig() {
	return {
		openAIKey: env.LLM_OPENAI_API_KEY || '',
		openAIModel: env.LLM_OPENAI_MODEL || 'gpt-4o-mini',
		maxExamples: Number.parseInt(env.LLM_MAX_EXAMPLES || '8'),
		temperature: Number.parseFloat(env.LLM_TEMPERATURE || '0.7'),
		maxTokens: Number.parseInt(env.LLM_MAX_TOKENS || '1000'),
	};
}

// LLM Optimization configuration
export async function getLLMOptimizationConfig() {
	return {
		enabled: env.LLM_OPTIMIZATION_ENABLED === 'true',
		promptOptimization: {
			enabled: env.LLM_PROMPT_OPTIMIZATION_ENABLED !== 'false', // Default true
			compressionTarget: Number.parseFloat(env.LLM_COMPRESSION_TARGET || '0.4'), // 40% reduction target
		},
		caching: {
			enabled: env.LLM_CACHING_ENABLED !== 'false', // Default true
			ttl: {
				vocabulary: Number.parseInt(env.LLM_CACHE_TTL_VOCABULARY || '604800'), // 7 days
				wordDetails: Number.parseInt(env.LLM_CACHE_TTL_WORD_DETAILS || '604800'), // 7 days
				paragraphs: Number.parseInt(env.LLM_CACHE_TTL_PARAGRAPHS || '259200'), // 3 days
				questions: Number.parseInt(env.LLM_CACHE_TTL_QUESTIONS || '259200'), // 3 days
				evaluations: Number.parseInt(env.LLM_CACHE_TTL_EVALUATIONS || '2592000'), // 30 days
				grammarPractice: Number.parseInt(env.LLM_CACHE_TTL_GRAMMAR || '86400'), // 1 day
			},
			semanticSimilarity: {
				enabled: env.LLM_SEMANTIC_CACHE_ENABLED === 'true',
				threshold: Number.parseFloat(env.LLM_SEMANTIC_CACHE_THRESHOLD || '0.8'),
			},
		},
		tokenManagement: {
			budgetLimits: {
				daily: Number.parseInt(env.LLM_TOKEN_BUDGET_DAILY || '100000'),
				monthly: Number.parseInt(env.LLM_TOKEN_BUDGET_MONTHLY || '2500000'),
			},
			costAlerts: {
				dailyThreshold: Number.parseFloat(env.LLM_COST_ALERT_DAILY || '10.0'), // USD
				monthlyThreshold: Number.parseFloat(env.LLM_COST_ALERT_MONTHLY || '250.0'), // USD
			},
			estimation: {
				enabled: env.LLM_TOKEN_ESTIMATION_ENABLED !== 'false', // Default true
				trackActualUsage: env.LLM_TRACK_ACTUAL_USAGE === 'true',
			},
		},
		monitoring: {
			enabled: env.LLM_MONITORING_ENABLED === 'true',
			logLevel: env.LLM_LOG_LEVEL || 'info', // debug, info, warn, error
			metricsCollection: env.LLM_METRICS_COLLECTION === 'true',
		},
	};
}
