import { getLLMConfig, getLLMOptimizationConfig } from '@/config';
import { <PERSON>Word, RandomWordDetailSchema, RandomWordSchema } from '@/models';
import { Difficulty, Language, Word } from '@prisma/client';
import OpenAI from 'openai';
import { zodResponseFormat } from 'openai/helpers/zod';
import { z } from 'zod';
import { WordService } from '.';
import {
	getErrorDensityDescription,
	getErrorTypesForDifficulty,
} from '@/app/collections/[id]/paragraph/grammar-practice/constants';
import { PromptOptimizerService } from './prompt-optimizer.service';
import { CacheService } from './cache.service';
import { tokenMonitor } from './token-monitor.service';

// Interface for existing method, updated
interface GenerateParagraphParams {
	keywords: string[];
	language: Language;
	difficulty: Difficulty;
	count: number; // Added count for multiple paragraphs
	sentenceCount?: number; // desired number of sentences
}

// Interfaces for existing methods (remains the same, but listed for completeness)
interface GenerateRandomTermsParams {
	userId: string;
	keywordTerms: string[];
	excludesTerms: string[];
	maxTerms: number;
	excludeCollectionIds: string[];
	source_language: Language;
	target_language: Language;
}

interface GenerateExercisesParams {
	paragraph: string;
	keywords: string[];
	language: Language;
	difficulty: Difficulty;
}

export interface AnalyzeParagraphRequest {
	content: string;
	language: Language;
}

// Interfaces for Question & Answer features
export interface GenerateQuestionsParams {
	paragraph: string;
	language: Language;
	questionCount: number;
}

// Interface for combined paragraph and questions generation
export interface GenerateParagraphWithQuestionsParams {
	keywords: string[];
	language: Language;
	difficulty: Difficulty;
	sentenceCount?: number;
	questionCount: number;
}

export interface ParagraphWithQuestionsResult {
	paragraph: string;
	questions: string[];
}

export interface EvaluateAnswersParams {
	paragraph: string;
	questions: string[];
	answers: string[];
	qna_language: Language; // Language of the questions and answers
	feedback_native_language: Language; // User's native language for feedback
}

export interface GrammarPracticeParams {
	keywords: string[];
	language: Language;
	source_language: Language;
	target_language: Language;
	difficulty: Difficulty;
	count: number;
	sentenceCount?: number;
	errorDensity?: 'low' | 'medium' | 'high';
}

export interface GrammarPracticeResultItem {
	paragraphWithErrors: string;
	correctedParagraph: string;
	allErrors: Array<{
		errorText: string;
		correctedText: string;
		errorType: string;
		explanation: {
			source_language: string;
			target_language: string;
		};
	}>;
}

export interface AnswerEvaluationResult {
	question: string;
	answer: string;
	feedback: {
		qna_feedback_text: string; // Feedback in the Q&A language
		native_feedback_text: string; // Feedback in the user's native language
	};
	score: number | null;
	is_correct: boolean | null;
	suggested_answer: string | null;
}

// Interfaces for the new evaluateTranslation method
export interface EvaluateTranslationParams {
	original_text: string;
	translated_text: string;
	source_language: Language;
	target_language: Language;
}

export interface TranslationEvaluationResult {
	feedback: {
		source_language: string;
		target_language: string;
	};
	score: number | null;
	suggestions: {
		source_language: string[] | null;
		target_language: string[] | null;
	} | null;
}

// Zod schemas for new/updated methods
const ExerciseSchema = z.object({
	type: z.string(),
	question: z.string(),
	answer: z.string(),
	options: z.array(z.string()).nullable(),
	explanation: z.string().nullable(),
});

const ExercisesResponseSchema = z.object({
	exercises: z.array(ExerciseSchema),
});

// Zod schema for generating questions
const GeneratedQuestionsSchema = z.object({
	questions: z
		.array(z.string())
		.describe('An array of generated questions based on the paragraph.'),
});

// Zod schema for combined paragraph and questions generation
const ParagraphWithQuestionsSchema = z.object({
	paragraph: z.string().describe('A generated paragraph based on the provided keywords.'),
	questions: z
		.array(z.string())
		.describe('An array of generated questions based on the paragraph.'),
});

// Zod schema for evaluating a single answer
const AnswerEvaluationSchema = z.object({
	question: z.string().describe('The original question this evaluation pertains to.'),
	answer: z.string().describe("The user's answer that was evaluated."),
	feedback: z
		.object({
			qna_feedback_text: z
				.string()
				.describe(
					"Detailed feedback in the Q&A language ({qna_language}) on the user's answer, explaining its strengths and weaknesses."
				),
			native_feedback_text: z
				.string()
				.describe(
					"The same detailed feedback, but translated or adapted into the user's native language ({feedback_native_language}) for better understanding."
				),
		})
		.describe(
			"Detailed feedback on the user's answer, provided in both the Q&A language and the user's native language."
		),
	score: z
		.number()
		.min(1)
		.max(5)
		.nullable()
		.describe('A score from 1 (poor) to 5 (excellent) for the answer.'),
	is_correct: z
		.boolean()
		.nullable()
		.describe(
			'A simple true/false indicating if the answer is fundamentally correct regarding the paragraph content.'
		),
	suggested_answer: z
		.string()
		.nullable()
		.describe(
			'An example of an ideal or improved answer to the question, based on the paragraph.'
		),
});

// Zod schema for evaluating all answers
const AllAnswersEvaluationSchema = z.object({
	evaluations: z
		.array(AnswerEvaluationSchema)
		.describe('An array of evaluations, one for each question-answer pair.'),
});

const TranslationEvaluationSchema = z.object({
	feedback: z
		.object({
			source_language: z
				.string()
				.describe('Overall feedback in the original text language (source_language).'),
			target_language: z
				.string()
				.describe("Overall feedback in the user's translation language (target_language)."),
		})
		.describe(
			"Overall feedback on the translation's quality, correctness, and areas for improvement, provided in both source and target languages."
		),
	score: z
		.number()
		.min(1)
		.max(10)
		.nullable()
		.describe('An overall score from 1 (poor) to 10 (excellent) for the translation.'),
	suggestions: z
		.object({
			source_language: z
				.array(z.string())
				.nullable()
				.describe(
					'Specific suggestions for improving the translation in the source_language, if any.'
				),
			target_language: z
				.array(z.string())
				.nullable()
				.describe(
					'Specific suggestions for improving the translation in the target_language, if any.'
				),
		})
		.nullable()
		.describe(
			'Specific suggestions for improving the translation, provided in both source and target languages, if any.'
		),
});

// New combined schema for grammar practice
const GrammarPracticeResponseSchema = z.object({
	paragraphs: z.array(
		z.object({
			paragraphWithErrors: z
				.string()
				.describe('Array of paragraphs containing intentional errors for practice.'),
			correctedParagraph: z
				.string()
				.describe('Array of the same paragraphs with all errors corrected.'),
			allErrors: z
				.array(
					z.object({
						errorText: z
							.string()
							.describe('The incorrect text found in the paragraph.'),
						correctedText: z.string().describe('The corrected version of the error.'),
						errorType: z
							.string()
							.describe('Type of error (grammar, spelling, word choice, etc.).'),
						explanation: z
							.object({
								source_language: z
									.string()
									.describe(
										'Explanation of the error and correction in the source language.'
									),
								target_language: z
									.string()
									.describe(
										'Explanation of the error and correction in the target language.'
									),
							})
							.describe('Dual-language explanations of the error and correction.'),
					})
				)
				.describe(
					'All errors found in all paragraphs with their corrections and explanations.'
				),
		})
	),
});

export class LLMService {
	private openai: OpenAI | null = null;
	private initPromise: Promise<void> | null = null;
	private cacheService: CacheService;
	private optimizationConfig: any = null;

	constructor(private readonly getWordService: () => WordService) {
		this.initPromise = this.initializeOpenAI();
		this.cacheService = new CacheService();
		this.loadOptimizationConfig();
	}

	private async loadOptimizationConfig(): Promise<void> {
		this.optimizationConfig = await getLLMOptimizationConfig();
	}

	private async initializeOpenAI(): Promise<void> {
		const llmConfig = await getLLMConfig();
		this.openai = new OpenAI({ apiKey: llmConfig.openAIKey });
	}

	private async ensureInitialized(): Promise<OpenAI> {
		if (this.initPromise) {
			await this.initPromise;
			this.initPromise = null;
		}
		if (!this.openai) {
			throw new Error('OpenAI client not initialized');
		}
		return this.openai;
	}

	/**
	 * Helper method to optimize prompts and handle caching
	 */
	private async optimizedLLMCall<T>(
		operation: string,
		templateKey: string,
		params: Record<string, any>,
		openaiParams: any,
		userId?: string
	): Promise<T> {
		// Generate cache key
		const cacheKey = this.cacheService.generateLLMKey(operation, params);

		// Check cache first if enabled
		if (this.optimizationConfig?.caching?.enabled) {
			const cached = this.cacheService.get<T>(cacheKey);
			if (cached) {
				return cached;
			}
		}

		// Optimize prompt if enabled
		let systemPrompt = params.systemPrompt;
		let optimized = false;
		let compressionRatio = 1;

		if (this.optimizationConfig?.promptOptimization?.enabled) {
			try {
				const optimization = PromptOptimizerService.optimizePrompt(templateKey, params);
				systemPrompt = optimization.optimizedPrompt;
				optimized = true;
				compressionRatio = optimization.compressionRatio;
			} catch (error) {
				console.warn(`Failed to optimize prompt for ${templateKey}:`, error);
				// Fall back to original prompt
			}
		}

		// Make API call
		const openai = await this.ensureInitialized();
		const llmConfig = await getLLMConfig();

		const completion = await openai.chat.completions.create({
			...openaiParams,
			model: llmConfig.openAIModel,
			messages: [{ role: 'system', content: systemPrompt }],
		});

		// Track token usage
		const inputTokens = PromptOptimizerService.estimateTokens(systemPrompt);
		const outputTokens = PromptOptimizerService.estimateTokens(
			completion.choices[0]?.message.content || ''
		);

		tokenMonitor.trackUsage({
			endpoint: operation,
			operation: templateKey,
			inputTokens,
			outputTokens,
			model: llmConfig.openAIModel,
			userId,
			optimized,
			compressionRatio,
		});

		// Parse result
		const result = JSON.parse(completion.choices[0]?.message.content || '{}');

		// Cache result if enabled
		if (this.optimizationConfig?.caching?.enabled) {
			const ttl = CacheService.getOptimizedTTL(templateKey);
			this.cacheService.setOptimized(cacheKey, result, {
				ttl,
				tags: [operation, templateKey],
				priority: this.getCachePriority(operation),
			});
		}

		return result;
	}

	/**
	 * Get cache priority based on operation type
	 */
	private getCachePriority(operation: string): 'high' | 'normal' | 'low' {
		const highPriorityOps = ['generateWordDetails', 'evaluateTranslation'];
		const lowPriorityOps = ['generateGrammarPractice'];

		if (highPriorityOps.includes(operation)) return 'high';
		if (lowPriorityOps.includes(operation)) return 'low';
		return 'normal';
	}

	private getLanguageName(language: Language): string {
		switch (language) {
			case 'EN':
				return 'English language';
			case 'VI':
				return 'Vietnamese language';
			default:
				throw new Error(`Unsupported language: ${language}`);
		}
	}

	async generateRandomTerms(params: GenerateRandomTermsParams): Promise<RandomWord[]> {
		const { keywordTerms, excludesTerms, maxTerms, excludeCollectionIds } = params;

		const allFetchedWords: Word[] = (
			await Promise.all(
				excludeCollectionIds.map(async (id) =>
					this.getWordService().getWordsByCollection(params.userId, id)
				)
			)
		).flat();

		const seenIds = new Set<string | number>();
		const excludeCollectionsWords: Word[] = allFetchedWords.filter((word) => {
			const wordId = word.id;
			if (seenIds.has(wordId)) {
				return false;
			}
			seenIds.add(wordId);
			return true;
		});

		const allExcludes = [
			...excludesTerms,
			...excludeCollectionsWords.map((word: Word) => word.term),
		];

		const openai = await this.ensureInitialized();
		const llmConfig = await getLLMConfig();
		const completion = await openai.chat.completions.create({
			model: llmConfig.openAIModel,
			messages: [
				{
					role: 'system',
					content: `Generate ${maxTerms} ${this.getLanguageName(
						params.target_language
					)} vocabulary terms for ${this.getLanguageName(
						params.source_language
					)} native speakers.

Keywords: ${keywordTerms.join(', ')}
Exclude: ${allExcludes.join(', ')}
Format: lowercase (except proper nouns)
Types: mix nouns, verbs, adjectives
Level: appropriate for language learners

Return thematically consistent terms only.`,
				},
			],
			temperature: 0.6,
			max_tokens: 6000,
			response_format: zodResponseFormat(
				z.object({
					words: z.array(RandomWordSchema),
				}),
				'event'
			),
		});

		const randomWords = JSON.parse(completion.choices[0]?.message.content || '[]');
		if (!randomWords) {
			throw new Error('Failed to generate terms');
		}

		return randomWords.words as RandomWord[];
	}

	async generateWordDetails(
		terms: string[],
		source_language: Language,
		target_language: Language
	): Promise<Word[]> {
		const existingWords = await this.getWordService().getWordsByTerms(terms);
		const results: Word[] = [];

		const termsToProcess = new Set(terms);

		for (const word of existingWords) {
			results.push(word);
			termsToProcess.delete(word.term);
		}

		if (termsToProcess.size > 0) {
			const openai = await this.ensureInitialized();
			const llmConfig = await getLLMConfig();
			const completion = await openai.chat.completions.create({
				model: llmConfig.openAIModel,
				messages: [
					{
						role: 'system',
						content: `Create word details for: ${Array.from(termsToProcess).join(', ')}

For each term:
1. IPA phonetic transcription
2. Definitions in ${this.getLanguageName(target_language)} and ${this.getLanguageName(
							source_language
						)}
3. 2-3 example sentences in both languages

Focus on accuracy and practical usage for ${this.getLanguageName(
							source_language
						)} speakers learning ${this.getLanguageName(target_language)}.`,
					},
				],
				temperature: 0.5,
				max_tokens: 12000,
				response_format: zodResponseFormat(
					z.object({
						words: z.array(RandomWordDetailSchema),
					}),
					'wordDetail'
				),
			});

			const details = JSON.parse(completion.choices[0]?.message.content || '{}');
			if (!details) {
				throw new Error('Failed to generate word details');
			}

			for (const wordDetail of details.words) {
				results.push(
					await this.getWordService().createWordWithRandomWordDetail(wordDetail)
				);
			}
		}

		return results;
	}

	async generateParagraph(params: GenerateParagraphParams): Promise<string[]> {
		const { keywords, language, difficulty, count, sentenceCount } = params;
		const systemPrompt = `Create ${count} ${this.getLanguageName(
			language
		)} paragraphs for ${difficulty.toLowerCase()} learners.

Themes: ${keywords.join(', ')}
${sentenceCount ? `Length: ~${sentenceCount} sentences each` : 'Length: appropriate'}
Level: ${difficulty.toLowerCase()} vocabulary
Purpose: translation practice

Requirements:
- Engaging, culturally appropriate
- Perfect grammar
- Each paragraph unique
- Meaningful content for vocabulary retention`;

		const responseSchema = z.object({
			paragraphs: z.array(z.string()),
		});

		const maxRetries = 3;
		let retryCount = 0;
		let generatedParagraphs: string[] | null = null;

		while (retryCount < maxRetries && !generatedParagraphs) {
			try {
				const openai = await this.ensureInitialized();
				const llmConfig = await getLLMConfig();
				const completion = await openai.chat.completions.create({
					model: llmConfig.openAIModel,
					messages: [
						{
							role: 'system',
							content: systemPrompt,
						},
					],
					temperature: 0.7,
					max_tokens: 1000 * count, // Consider adjusting if sentenceCount implies longer paragraphs
					response_format: zodResponseFormat(responseSchema, 'generateParagraphs'),
				});

				const parsedResponse = JSON.parse(completion.choices[0]?.message.content || '{}');

				if (
					!parsedResponse ||
					!parsedResponse.paragraphs ||
					parsedResponse.paragraphs.length !== count
				) {
					throw new Error(
						`LLM did not return the expected number of paragraphs. Expected ${count}, got ${
							parsedResponse?.paragraphs?.length || 0
						}.`
					);
				}
				generatedParagraphs = parsedResponse.paragraphs;
			} catch (error) {
				retryCount++;
				console.error(`OpenAI API error (attempt ${retryCount}/${maxRetries}):`, error);

				if (retryCount >= maxRetries) {
					throw new Error(
						`Failed to generate paragraphs after ${maxRetries} attempts: ${
							error instanceof Error ? error.message : String(error)
						}`
					);
				}

				const delay = Math.pow(2, retryCount) * 1000;
				await new Promise((resolve) => setTimeout(resolve, delay));
			}
		}

		if (!generatedParagraphs) {
			throw new Error('Failed to generate paragraphs after retries.');
		}

		return generatedParagraphs;
	}

	async generateExercises(
		params: GenerateExercisesParams
	): Promise<z.infer<typeof ExerciseSchema>[]> {
		const { paragraph, keywords, language, difficulty } = params;

		const systemPrompt = `Create 3-5 exercises from this ${this.getLanguageName(
			language
		)} paragraph for ${difficulty.toLowerCase()} learners:
"${paragraph}"

Keywords: ${keywords.join(', ')}
Types: Fill in the Blank, Multiple Choice, Matching
Test: vocabulary, grammar, comprehension

Requirements:
- Clear questions with single correct answers
- Multiple choice: 3-4 options
- Include explanations
- Progressive difficulty
- Based on paragraph content only`;

		const maxRetries = 3;
		let retryCount = 0;
		let generatedExercises: z.infer<typeof ExerciseSchema>[] | null = null;

		while (retryCount < maxRetries && !generatedExercises) {
			try {
				const openai = await this.ensureInitialized();
				const llmConfig = await getLLMConfig();
				const completion = await openai.chat.completions.create({
					model: llmConfig.openAIModel,
					messages: [
						{
							role: 'system',
							content: systemPrompt,
						},
					],
					temperature: 0.7,
					max_tokens: 2000,
					response_format: zodResponseFormat(
						ExercisesResponseSchema,
						'generateExercises'
					),
				});

				const parsedResponse = JSON.parse(completion.choices[0]?.message.content || '{}');
				if (!parsedResponse || !parsedResponse.exercises) {
					throw new Error('Empty or invalid response from LLM for exercises.');
				}
				generatedExercises = parsedResponse.exercises;
			} catch (error) {
				retryCount++;
				console.error(`OpenAI API error (attempt ${retryCount}/${maxRetries}):`, error);
				if (retryCount >= maxRetries) {
					throw new Error(
						`Failed to generate exercises after ${maxRetries} attempts: ${
							error instanceof Error ? error.message : String(error)
						}`
					);
				}
				const delay = Math.pow(2, retryCount) * 1000;
				await new Promise((resolve) => setTimeout(resolve, delay));
			}
		}

		if (!generatedExercises) {
			throw new Error('Failed to generate exercises after retries.');
		}

		return generatedExercises;
	}

	async evaluateTranslation(
		params: EvaluateTranslationParams
	): Promise<TranslationEvaluationResult> {
		const { original_text, translated_text, source_language, target_language } = params;

		const systemPrompt = `Evaluate translation quality (1-10 scale):

Original (${this.getLanguageName(source_language)}): "${original_text}"
Translation (${this.getLanguageName(target_language)}): "${translated_text}"

Assess: accuracy, grammar, fluency, cultural appropriateness

Score guide:
- 9-10: Excellent
- 7-8: Good (minor issues)
- 5-6: Adequate (needs improvement)
- 3-4: Poor (significant issues)
- 1-2: Very poor

Provide constructive feedback in both languages with specific suggestions for improvement.`;

		try {
			const openai = await this.ensureInitialized();
			const llmConfig = await getLLMConfig();
			const completion = await openai.chat.completions.create({
				model: llmConfig.openAIModel,
				messages: [
					{
						role: 'system',
						content: systemPrompt,
					},
				],
				temperature: 0.3,
				max_tokens: 1000,
				response_format: zodResponseFormat(
					TranslationEvaluationSchema,
					'evaluateTranslation'
				),
			});

			const evaluationResult = JSON.parse(completion.choices[0]?.message.content || '{}');

			if (!evaluationResult) {
				throw new Error('LLM did not return a valid evaluation.');
			}
			return evaluationResult as TranslationEvaluationResult;
		} catch (error) {
			console.error('Error evaluating translation with OpenAI:', error);
			throw new Error(
				`Failed to evaluate translation: ${
					error instanceof Error ? error.message : String(error)
				}`
			);
		}
	}

	async generateQuestions(params: GenerateQuestionsParams): Promise<string[]> {
		const { paragraph, language, questionCount } = params;

		const systemPrompt = `Create ${questionCount} comprehension questions for this ${this.getLanguageName(
			language
		)} paragraph:
"${paragraph}"

Question types: literal (30-40%), inferential (40-50%), analytical (10-20%)
Formats: who, what, why, how (avoid yes/no)
Content: main ideas, details, vocabulary, cause/effect

Requirements:
- Clear wording for ${this.getLanguageName(language)} learners
- Answerable from paragraph only
- Varied difficulty levels
- Promote understanding`;

		const maxRetries = 3;
		let retryCount = 0;
		let generatedQuestions: string[] | null = null;

		while (retryCount < maxRetries && !generatedQuestions) {
			try {
				const openai = await this.ensureInitialized();
				const llmConfig = await getLLMConfig();
				const completion = await openai.chat.completions.create({
					model: llmConfig.openAIModel,
					messages: [{ role: 'system', content: systemPrompt }],
					temperature: 0.6,
					max_tokens: 500 + questionCount * 100, // Adjusted token limit
					response_format: zodResponseFormat(
						GeneratedQuestionsSchema,
						'generateQuestions'
					),
				});

				const parsedResponse = JSON.parse(completion.choices[0]?.message.content || '{}');

				if (
					!parsedResponse ||
					!parsedResponse.questions ||
					parsedResponse.questions.length !== questionCount
				) {
					throw new Error(
						`LLM did not return the expected number of questions. Expected ${questionCount}, got ${
							parsedResponse?.questions?.length || 0
						}.`
					);
				}
				generatedQuestions = parsedResponse.questions;
			} catch (error) {
				retryCount++;
				console.error(
					`OpenAI API error (generateQuestions, attempt ${retryCount}/${maxRetries}):`,
					error
				);
				if (retryCount >= maxRetries) {
					throw new Error(
						`Failed to generate questions after ${maxRetries} attempts: ${
							error instanceof Error ? error.message : String(error)
						}`
					);
				}
				const delay = Math.pow(2, retryCount) * 1000;
				await new Promise((resolve) => setTimeout(resolve, delay));
			}
		}

		if (!generatedQuestions) {
			throw new Error('Failed to generate questions after retries.');
		}
		return generatedQuestions;
	}

	async generateParagraphWithQuestions(
		params: GenerateParagraphWithQuestionsParams
	): Promise<ParagraphWithQuestionsResult> {
		const { keywords, language, difficulty, sentenceCount, questionCount } = params;

		const sentenceRequirement = sentenceCount
			? `The paragraph should have approximately ${sentenceCount} sentences.`
			: '';

		const systemPrompt = `Create 1 ${this.getLanguageName(
			language
		)} paragraph for ${difficulty.toLowerCase()} learners + ${questionCount} questions.

Paragraph:
- Keywords: ${keywords.join(', ')}
${sentenceRequirement ? `- ${sentenceRequirement}` : ''}
- Perfect grammar, ${difficulty.toLowerCase()} vocabulary
- Engaging, culturally appropriate

Questions:
- Types: literal (30-40%), inferential (40-50%), analytical (10-20%)
- Cover: main ideas, details, vocabulary, cause/effect
- Clear wording, answerable from paragraph
- Varied difficulty, no yes/no questions

Create integrated learning unit.`;

		const maxRetries = 3;
		let retryCount = 0;
		let result: ParagraphWithQuestionsResult | null = null;

		while (retryCount < maxRetries && !result) {
			try {
				const openai = await this.ensureInitialized();
				const llmConfig = await getLLMConfig();
				const completion = await openai.chat.completions.create({
					model: llmConfig.openAIModel,
					messages: [{ role: 'system', content: systemPrompt }],
					temperature: 0.7,
					max_tokens: 1500 + questionCount * 100, // Adjusted token limit
					response_format: zodResponseFormat(
						ParagraphWithQuestionsSchema,
						'generateParagraphWithQuestions'
					),
				});

				const parsedResponse = JSON.parse(completion.choices[0]?.message.content || '{}');

				if (
					!parsedResponse ||
					!parsedResponse.paragraph ||
					!parsedResponse.questions ||
					parsedResponse.questions.length !== questionCount
				) {
					throw new Error(
						`LLM did not return the expected format. Expected 1 paragraph and ${questionCount} questions, got paragraph: ${
							parsedResponse?.paragraph ? 'yes' : 'no'
						}, questions: ${parsedResponse?.questions?.length || 0}.`
					);
				}

				result = {
					paragraph: parsedResponse.paragraph,
					questions: parsedResponse.questions,
				};
			} catch (error) {
				retryCount++;
				console.error(
					`OpenAI API error (generateParagraphWithQuestions, attempt ${retryCount}/${maxRetries}):`,
					error
				);
				if (retryCount >= maxRetries) {
					throw new Error(
						`Failed to generate paragraph with questions after ${maxRetries} attempts: ${
							error instanceof Error ? error.message : String(error)
						}`
					);
				}
				const delay = Math.pow(2, retryCount) * 1000;
				await new Promise((resolve) => setTimeout(resolve, delay));
			}
		}

		if (!result) {
			throw new Error('Failed to generate paragraph with questions after retries.');
		}
		return result;
	}

	async evaluateAnswers(params: EvaluateAnswersParams): Promise<AnswerEvaluationResult[]> {
		const { paragraph, questions, answers, qna_language, feedback_native_language } = params;

		if (questions.length !== answers.length) {
			throw new Error('The number of questions and answers must match.');
		}
		if (questions.length === 0) {
			return [];
		}

		let questionAnswerPairs = '';
		questions.forEach((q, i) => {
			questionAnswerPairs += `Question ${i + 1}: "${q}"\nUser's Answer ${i + 1}: "${
				answers[i]
			}"\n\n`;
		});

		const systemPrompt = `Evaluate answers based on this paragraph:

Reference (${this.getLanguageName(qna_language)}): "${paragraph}"

Question-Answer Pairs:
${questionAnswerPairs}

Assess: accuracy, completeness, language quality, understanding

Score (1-5):
- 5: Excellent
- 4: Good (minor issues)
- 3: Satisfactory (some inaccuracies)
- 2: Needs improvement
- 1: Poor

Correctness: True if fundamentally accurate

Provide constructive feedback in ${this.getLanguageName(qna_language)} and ${this.getLanguageName(
			feedback_native_language
		)}. Include suggested answers when helpful.`;

		const maxRetries = 3;
		let retryCount = 0;
		let evaluationResults: AnswerEvaluationResult[] | null = null;

		while (retryCount < maxRetries && !evaluationResults) {
			try {
				const openai = await this.ensureInitialized();
				const llmConfig = await getLLMConfig();
				const completion = await openai.chat.completions.create({
					model: llmConfig.openAIModel,
					messages: [{ role: 'system', content: systemPrompt }],
					temperature: 0.3,
					max_tokens: 1500 + questions.length * 300, // Adjusted token limit
					response_format: zodResponseFormat(
						AllAnswersEvaluationSchema,
						'evaluateAnswers'
					),
				});

				const parsedResponse = JSON.parse(completion.choices[0]?.message.content || '{}');

				if (
					!parsedResponse ||
					!parsedResponse.evaluations ||
					parsedResponse.evaluations.length !== questions.length
				) {
					throw new Error(
						`LLM did not return the expected number of evaluations. Expected ${
							questions.length
						}, got ${parsedResponse?.evaluations?.length || 0}.`
					);
				}
				evaluationResults = parsedResponse.evaluations as AnswerEvaluationResult[];
			} catch (error) {
				retryCount++;
				console.error(
					`OpenAI API error (evaluateAnswers, attempt ${retryCount}/${maxRetries}):`,
					error
				);
				if (retryCount >= maxRetries) {
					throw new Error(
						`Failed to evaluate answers after ${maxRetries} attempts: ${
							error instanceof Error ? error.message : String(error)
						}`
					);
				}
				const delay = Math.pow(2, retryCount) * 1000;
				await new Promise((resolve) => setTimeout(resolve, delay));
			}
		}
		if (!evaluationResults) {
			throw new Error('Failed to evaluate answers after retries.');
		}
		return evaluationResults;
	}

	async generateGrammarPractice(
		params: GrammarPracticeParams
	): Promise<GrammarPracticeResultItem[]> {
		const {
			keywords,
			language,
			source_language: _source_language,
			target_language: _target_language,
			difficulty,
			count,
			sentenceCount,
			errorDensity = 'medium',
		} = params;

		const sentenceRequirement = sentenceCount
			? `Each paragraph should have approximately ${sentenceCount} sentences.`
			: '';

		const errorTypes = getErrorTypesForDifficulty(difficulty);
		const densityDescription = getErrorDensityDescription(errorDensity, difficulty);

		// Create detailed error examples based on difficulty
		const getErrorExamples = (difficulty: string) => {
			switch (difficulty) {
				case 'BEGINNER':
					return `
- Spelling errors: "recieve" → "receive", "seperate" → "separate"
- Capitalization: "i am happy" → "I am happy", "new york" → "New York"
- Basic verb tense: "She go to school" → "She goes to school"
- Subject-verb agreement: "He like music" → "He likes music"
- Basic articles: "a apple" → "an apple", "I need book" → "I need a book"
- Basic punctuation: "How are you." → "How are you?"`;
				case 'INTERMEDIATE':
					return `
- Preposition usage: "good in English" → "good at English", "depend of" → "depend on"
- Conjunction errors: "Although it was raining, but we went out" → "Although it was raining, we went out"
- Sentence fragments: "Because I was tired." → "Because I was tired, I went to bed early."
- Run-on sentences: "I went to the store I bought milk" → "I went to the store, and I bought milk"
- Passive voice: "The cake ate by me" → "The cake was eaten by me"
- Word form errors: "He was boring" → "He was bored", "I am interesting in music" → "I am interested in music"`;
				case 'ADVANCED':
					return `
- Reported speech: "She said she is happy" → "She said she was happy"
- Conditional sentences: "If I will go there" → "If I go there"
- Register appropriateness: Using "Hey dude" in formal writing → "Dear Sir/Madam"
- Word choice and semantics: "He committed a mistake" → "He made a mistake"
- Sentence ambiguity: "She told her friend that she loved her" → "She told her friend that she (the friend) loved her"
- Word order: "Beautiful is she" → "She is beautiful"`;
				default:
					return getErrorExamples('INTERMEDIATE');
			}
		};

		const errorExamples = getErrorExamples(difficulty);

		const systemPrompt = `Create ${count} grammar practice paragraphs in ${language} for ${difficulty.toLowerCase()} level learners.

CRITICAL REQUIREMENTS:

1. KEYWORD INTEGRATION (MANDATORY):
   - MUST relate to ALL provided keywords: ${keywords.join(', ')}

2. ERROR REQUIREMENTS:
   - Error density: ${densityDescription}
   - Focus ONLY on these error types for ${difficulty} level: ${errorTypes.join(', ')}

   SPECIFIC ERROR EXAMPLES TO FOLLOW:${errorExamples}

3. CONTENT STRUCTURE:
   ${
		sentenceRequirement
			? `${sentenceRequirement}`
			: 'Each paragraph should be 3-5 sentences long.'
   }
   - Create engaging, coherent content that naturally incorporates the keywords
   - Ensure the topic is relevant and interesting for language learners
   - Make sure the content flows logically

4. OUTPUT FORMAT:
   For each paragraph provide:
   - paragraphWithErrors: The paragraph containing intentional errors
   - correctedParagraph: The same paragraph with all errors fixed
   - allErrors: Array of error objects with errorText, correctedText, errorType, and explanations

5. ERROR EXPLANATIONS:
   - Provide clear explanations in both ${this.getLanguageName(
		params.source_language
   )} and ${this.getLanguageName(params.target_language)}
   - Explain WHY the error is wrong and WHY the correction is right

EXAMPLE WORKFLOW:
1. Choose a topic that naturally incorporates the keywords: ${keywords.join(', ')}
2. Write a coherent paragraph about this topic
3. Introduce the specified error types naturally within the content
4. Ensure errors match the difficulty level and examples provided above`;

		const maxRetries = 3;
		let retryCount = 0;
		let result: GrammarPracticeResultItem[] = [];

		while (retryCount < maxRetries && !result.length) {
			try {
				const openai = await this.ensureInitialized();
				const llmConfig = await getLLMConfig();
				const completion = await openai.chat.completions.create({
					model: llmConfig.openAIModel,
					messages: [{ role: 'system', content: systemPrompt }],
					temperature: 0.7,
					max_tokens: 2000 * count,
					response_format: zodResponseFormat(
						GrammarPracticeResponseSchema,
						'generateGrammarPractice'
					),
				});

				const parsedResponse = JSON.parse(completion.choices[0]?.message.content || '{}');
				if (!parsedResponse || !parsedResponse.paragraphs) {
					throw new Error(`LLM did not return the expected number of paragraphs`);
				}
				result = parsedResponse.paragraphs;
			} catch (error) {
				retryCount++;
				console.error(
					`OpenAI API error (generateGrammarPractice, attempt ${retryCount}/${maxRetries}):`,
					error
				);
				if (retryCount >= maxRetries) {
					throw new Error(
						`Failed to generate grammar practice after ${maxRetries} attempts: ${
							error instanceof Error ? error.message : String(error)
						}`
					);
				}
				const delay = Math.pow(2, retryCount) * 1000;
				await new Promise((resolve) => setTimeout(resolve, delay));
			}
		}

		if (!result) {
			throw new Error('Failed to generate grammar practice after retries.');
		}
		return result;
	}
}
