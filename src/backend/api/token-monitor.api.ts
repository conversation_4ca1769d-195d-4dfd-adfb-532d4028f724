import { NextRequest, NextResponse } from 'next/server';
import { tokenMonitor } from '@/backend/services/token-monitor.service';
import { verifyToken } from '@/backend/utils/token.util';
import { ValidationError, UnauthorizedError } from '@/backend/utils/errors';

/**
 * GET /api/token-monitor/stats
 * Get token usage statistics
 */
export async function getTokenStats(request: NextRequest): Promise<NextResponse> {
	try {
		// Verify authentication
		const authHeader = request.headers.get('authorization');
		if (!authHeader?.startsWith('Bearer ')) {
			throw new UnauthorizedError('Missing or invalid authorization header');
		}

		const token = authHeader.substring(7);
		await verifyToken(token);

		// Get query parameters
		const { searchParams } = new URL(request.url);
		const timeframe = searchParams.get('timeframe') as 'day' | 'week' | 'month' || 'day';

		// Validate timeframe
		if (!['day', 'week', 'month'].includes(timeframe)) {
			throw new ValidationError('Invalid timeframe. Must be day, week, or month');
		}

		const stats = tokenMonitor.getStats(timeframe);

		return NextResponse.json({
			success: true,
			data: stats,
			timeframe
		});
	} catch (error) {
		if (error instanceof ValidationError || error instanceof UnauthorizedError) {
			return NextResponse.json(
				{ success: false, error: error.message },
				{ status: error instanceof UnauthorizedError ? 401 : 400 }
			);
		}

		console.error('Error getting token stats:', error);
		return NextResponse.json(
			{ success: false, error: 'Internal server error' },
			{ status: 500 }
		);
	}
}

/**
 * GET /api/token-monitor/analysis
 * Get comprehensive cost analysis
 */
export async function getCostAnalysis(request: NextRequest): Promise<NextResponse> {
	try {
		// Verify authentication
		const authHeader = request.headers.get('authorization');
		if (!authHeader?.startsWith('Bearer ')) {
			throw new UnauthorizedError('Missing or invalid authorization header');
		}

		const token = authHeader.substring(7);
		await verifyToken(token);

		const analysis = tokenMonitor.getCostAnalysis();

		return NextResponse.json({
			success: true,
			data: analysis
		});
	} catch (error) {
		if (error instanceof ValidationError || error instanceof UnauthorizedError) {
			return NextResponse.json(
				{ success: false, error: error.message },
				{ status: error instanceof UnauthorizedError ? 401 : 400 }
			);
		}

		console.error('Error getting cost analysis:', error);
		return NextResponse.json(
			{ success: false, error: 'Internal server error' },
			{ status: 500 }
		);
	}
}

/**
 * GET /api/token-monitor/suggestions
 * Get optimization suggestions
 */
export async function getOptimizationSuggestions(request: NextRequest): Promise<NextResponse> {
	try {
		// Verify authentication
		const authHeader = request.headers.get('authorization');
		if (!authHeader?.startsWith('Bearer ')) {
			throw new UnauthorizedError('Missing or invalid authorization header');
		}

		const token = authHeader.substring(7);
		await verifyToken(token);

		const suggestions = tokenMonitor.getOptimizationSuggestions();

		return NextResponse.json({
			success: true,
			data: suggestions
		});
	} catch (error) {
		if (error instanceof ValidationError || error instanceof UnauthorizedError) {
			return NextResponse.json(
				{ success: false, error: error.message },
				{ status: error instanceof UnauthorizedError ? 401 : 400 }
			);
		}

		console.error('Error getting optimization suggestions:', error);
		return NextResponse.json(
			{ success: false, error: 'Internal server error' },
			{ status: 500 }
		);
	}
}

/**
 * GET /api/token-monitor/alerts
 * Check budget alerts
 */
export async function getBudgetAlerts(request: NextRequest): Promise<NextResponse> {
	try {
		// Verify authentication
		const authHeader = request.headers.get('authorization');
		if (!authHeader?.startsWith('Bearer ')) {
			throw new UnauthorizedError('Missing or invalid authorization header');
		}

		const token = authHeader.substring(7);
		await verifyToken(token);

		const alerts = tokenMonitor.checkBudgetAlerts();

		return NextResponse.json({
			success: true,
			data: alerts
		});
	} catch (error) {
		if (error instanceof ValidationError || error instanceof UnauthorizedError) {
			return NextResponse.json(
				{ success: false, error: error.message },
				{ status: error instanceof UnauthorizedError ? 401 : 400 }
			);
		}

		console.error('Error getting budget alerts:', error);
		return NextResponse.json(
			{ success: false, error: 'Internal server error' },
			{ status: 500 }
		);
	}
}

/**
 * POST /api/token-monitor/track
 * Manually track token usage (for testing)
 */
export async function trackTokenUsage(request: NextRequest): Promise<NextResponse> {
	try {
		// Verify authentication
		const authHeader = request.headers.get('authorization');
		if (!authHeader?.startsWith('Bearer ')) {
			throw new UnauthorizedError('Missing or invalid authorization header');
		}

		const token = authHeader.substring(7);
		await verifyToken(token);

		const body = await request.json();
		const { endpoint, operation, inputTokens, outputTokens, model, userId, optimized, compressionRatio } = body;

		// Validate required fields
		if (!endpoint || !operation || inputTokens === undefined || outputTokens === undefined || !model) {
			throw new ValidationError('Missing required fields: endpoint, operation, inputTokens, outputTokens, model');
		}

		// Validate numeric fields
		if (typeof inputTokens !== 'number' || typeof outputTokens !== 'number' || inputTokens < 0 || outputTokens < 0) {
			throw new ValidationError('inputTokens and outputTokens must be non-negative numbers');
		}

		tokenMonitor.trackUsage({
			endpoint,
			operation,
			inputTokens,
			outputTokens,
			model,
			userId,
			optimized: optimized || false,
			compressionRatio: compressionRatio || 1
		});

		return NextResponse.json({
			success: true,
			message: 'Token usage tracked successfully'
		});
	} catch (error) {
		if (error instanceof ValidationError || error instanceof UnauthorizedError) {
			return NextResponse.json(
				{ success: false, error: error.message },
				{ status: error instanceof UnauthorizedError ? 401 : 400 }
			);
		}

		console.error('Error tracking token usage:', error);
		return NextResponse.json(
			{ success: false, error: 'Internal server error' },
			{ status: 500 }
		);
	}
}

/**
 * GET /api/token-monitor/cache-stats
 * Get cache performance statistics
 */
export async function getCacheStats(request: NextRequest): Promise<NextResponse> {
	try {
		// Verify authentication
		const authHeader = request.headers.get('authorization');
		if (!authHeader?.startsWith('Bearer ')) {
			throw new UnauthorizedError('Missing or invalid authorization header');
		}

		const token = authHeader.substring(7);
		await verifyToken(token);

		// This would need to be implemented in the cache service
		// For now, return a placeholder
		const cacheStats = {
			hitRate: 0.75,
			totalRequests: 1000,
			hits: 750,
			misses: 250,
			memoryUsage: '15MB',
			keyCount: 150
		};

		return NextResponse.json({
			success: true,
			data: cacheStats
		});
	} catch (error) {
		if (error instanceof ValidationError || error instanceof UnauthorizedError) {
			return NextResponse.json(
				{ success: false, error: error.message },
				{ status: error instanceof UnauthorizedError ? 401 : 400 }
			);
		}

		console.error('Error getting cache stats:', error);
		return NextResponse.json(
			{ success: false, error: 'Internal server error' },
			{ status: 500 }
		);
	}
}
